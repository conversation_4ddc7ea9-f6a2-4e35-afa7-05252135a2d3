// Tipos para as requisições e respostas da API

// Auth
export interface LoginRequest {
  email: string;
  login_code: string;
}

export interface LoginResponse {
  access_token: string;
  refresh_token: string;
}

export interface LoginSuccessResponse {
  data: LoginResponse;
}

export interface SendLoginCodeRequest {
  email: string;
}

export interface RefreshRequest {
  refresh_token: string;
}

export interface RefreshResponse {
  token: string;
  refresh_token: string;
}

export interface RefreshSuccessResponse {
  data: RefreshResponse;
}

export interface EmptySuccessResponse {
  message?: string;
}

export interface ErrorResponse {
  message: string;
  code: string;
}

// Company
export interface Location {
  latitude: number;
  longitude: number;
}

export interface Address {
  city: string;
  complement?: string;
  external_id?: string;
  is_default: boolean;
  location?: Location;
  name?: string;
  neighborhood: string;
  number: string;
  state: string;
  street: string;
  zip_code: string;
}

export interface Company {
  id: string;
  external_id: string;
  name: string;
  document?: string;
  email?: string;
  phone_numbers: string[];
  status?: string;
  created_at: string;
  updated_at: string;
  bio?: string;
  cnpj?: string;
  picture?: string;
  pix_key?: string;
  address?: Address; // Legacy single address field
  addresses?: Address[]; // New addresses array from /details endpoint
  products?: Product[];
  is_active: boolean;
  delivery_modes?: string[];
  shipping_fee?: number;
  owner?: User;
  subscription_id?: number | null;
  rating?: number;
  affiliate_balance?: number;
  commission_rate?: number;
  cashback_rate?: number;
}

export interface CreateCompanyRequest {
  name: string;
  document: string;
  email: string;
  phone: string;
  bio?: string;
  cnpj?: string;
  picture?: string;
  pix_key?: string;
  address?: Address;
  delivery_modes?: string[];
  shipping_fee?: number;
}

export interface CreateCompanySuccessResponse {
  id: string;
  externalID: string;
  name: string;
  document: string;
  email: string;
  phone: string;
  status: string;
}

export interface GetActiveCompaniesSuccessResponse {
  data: Company[];
}

export interface GetActiveCompanySuccessResponse {
  data: Company;
}

export interface GetMyCompaniesResponse {
  company_external_ids: string[];
  dashboard_url: string;
  owner_external_id: string;
}

export interface GetMyCompaniesSuccessResponse {
  data: GetMyCompaniesResponse;
}

export interface CreateSubAccountRequest {
  companyId: string;
  name: string;
  document: string;
  email: string;
  phone: string;
}

// Product
export interface ProductCategory {
  name: string;
  image: string;
  external_id: string;
}

export interface Product {
  external_id: string;
  ean: string;
  name: string;
  image: string;  // Changed from imageUrl to image
  categories: ProductCategory[]; // Changed from string[] to ProductCategory[]
  brand: string;
  createdAt?: string;
  updatedAt?: string;
  is_reviewed: boolean;
  price: number;
  discount: number;
  stock: number;
}

export interface GetActiveProductsResponse {
  external_id: string;
  name: string;
  brand: string;
  ean: string;
  image: string;
  is_reviewed: boolean;
  is_18_plus: boolean;
  is_active: boolean;
  categories: {
    external_id: string;
    name: string;
    image: string;
  }[];
}

export interface GetActiveProductsSuccessPaginatedResponse {
  data: GetActiveProductsResponse[];
  limit: number;
  pageNumber: number;
  totalItems: number;
  totalPages: number;
}

export interface CreateProductSuccessResponse {
  id: string;
  ean: string;
  name: string;
  imageUrl: string;
  categories: string[];
  brand: string;
}

export interface AddProductsToCompanyRequest {
  productIds: string[];
}

// Interface para o contexto de autenticação
export interface AuthContextType {
  isAuthenticated: boolean;
  user: any;
  login: (data: LoginSuccessResponse) => Promise<void>;
  logout: () => Promise<void>;
}

export interface CreateCouponRequest {
  code: string;
  type: "percentage" | "fixed";
  value: number;
  min_order_value: number;
  quantity: number;
  expires_at: string;
  owner_type: "company" | "admin";
}

export interface Coupon {
  external_id: string;
  code: string;
  type: "percentage" | "fixed";
  value: number;
  min_order_value: number;
  quantity: number;
  is_active: boolean;
  expires_at: string;
  owner_type: "company" | "admin";
  owner_external_id: string;
  created_at: string;
  updated_at: string;
}

export interface ListCouponsSuccessResponse {
  data: Coupon[];
  limit: number;
  pageNumber: number;
  totalItems: number;
  totalPages: number;
}

export interface ListOneCouponSuccessResponse {
  data: Coupon;
}

export interface OrderProduct {
  quantity: number;
  price: number;
  discount: number;
  name: string;
  ean: string;
  brand: string;
  image: string;
  external_id: string;
}

export interface Order {
  order_id: string;
  status: "pending" | "processing" | "preparing" | "ready" | "delivering" | "completed" | "cancelled" | "expired";
  delivery_mode: "delivery" | "pickup"; // delivery mode for this specific order
  payment_method: string;
  amount: number; // in centavos
  discount: number; // in centavos - this might include coupon discount
  shipping_fee: number; // in centavos
  user_name: string;
  user_email: string;
  user_phone_number: string;
  user_address: string;
  products: OrderProduct[];
  created_at: string;
  updated_at: string;
  finished_at?: string; // when order is completed
  reason?: string; // for status changes (including cancellation reason)
  company_external_id: string; // Company that this order belongs to
  coupon?: string; // coupon code if applied
  coupon_discount?: number; // coupon discount amount in centavos
  coupon_type?: "company" | "admin"; // type of coupon applied
  // Alternative field names based on swagger documentation
  info?: string; // might contain additional information or cancellation reason
  status_description?: string; // might contain status-related information
}

export interface ListOrdersSuccessResponse {
  data: Order[];
  limit: number;
  pageNumber: number;
  totalItems: number;
  totalPages: number;
}

export interface User {
  external_id: string;
  name: string;
  email: string;
  phone_numbers: string[];
  cpf?: string; // CPF field from API
  document?: string; // Alternative CPF field (for compatibility)
  cashback_value?: number;
  subscription_id?: number;
  is_active?: boolean;
  is_deleted?: boolean;
  created_at: string;
  updated_at: string;
}

export interface SearchUsersSuccessResponse {
  data: User[];
}

export interface LinkUserToCompanyRequest {
  user_external_id: string;
}

export interface LinkUserToCompanySuccessResponse {
  message: string;
}

export interface UpdateCompanyStatusRequest {
  activate: boolean;
}

export interface UpdateCompanyStatusSuccessResponse {
  message: string;
}

// Balance and Withdrawal
export interface GetBalanceResponse {
  balance: number; // Value in centavos
  name: string;
  pix_key: string;
}

export interface GetBalanceSuccessResponse {
  data: GetBalanceResponse;
}

export interface WithdrawRequest {
  amount: number; // Value in centavos, minimum 50000 (R$ 500,00)
}

export interface WithdrawResponse {
  message: string;
}

export interface WithdrawSuccessResponse {
  data: WithdrawResponse;
}

// Individual withdrawal item
export interface WithdrawalItem {
  status: 'CREATED' | 'CONFIRMED' | 'FAILED'; // Status is now required
  amount: number; // Value in centavos
  correlation_id: string;
  destination_alias: string; // PIX destination
  comment?: string;
  end_to_end_id?: string;
  time: string; // ISO date string
  created_at: string; // ISO date string
  finished_at?: string; // Optional ISO date string - when withdrawal was 100% processed and completed
}

// Withdrawal History
export interface WithdrawalHistoryItem {
  withdrawals: WithdrawalItem[];
  total_amount: number; // Total amount withdrawn in centavos
}

export interface GetWithdrawalHistoryResponse {
  data: WithdrawalHistoryItem[];
  limit: number;
  pageNumber: number;
  totalItems: number;
  totalPages: number;
}

export interface GetWithdrawalHistorySuccessResponse {
  data: WithdrawalHistoryItem;
  limit: number;
  pageNumber: number;
  totalItems: number;
  totalPages: number;
}

// User Management Types
export interface CreateUserRequest {
  name: string;
  email: string;
  cpf: string;
  phone_numbers: string[];
}

export interface CreateUserSuccessResponse {
  data: User;
  message: string;
}

export interface CheckEmailExistsRequest {
  email: string;
}

export interface CheckEmailExistsResponse {
  data: boolean;
}

export interface UpdateUserStatusRequest {
  is_active: boolean;
}

export interface UpdateUserStatusSuccessResponse {
  message: string;
}

export interface UpdateOrderStatusRequest {
  status: "processing" | "preparing" | "ready" | "delivering" | "completed" | "cancelled";
  reason?: string;
}
